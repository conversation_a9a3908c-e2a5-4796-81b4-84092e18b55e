"use client"

import { createColumn<PERSON>elper } from "@tanstack/react-table";
import DataTable from "./common/data-table";
import { Person } from "@/types/component";

const mockSupabaseData: Person[] = [
  { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'Admin', status: 'Active', created_at: '2024-01-15' },
  { id: 2, name: '<PERSON>', email: '<EMAIL>', role: 'User', status: 'Active', created_at: '2024-02-20' },
  { id: 3, name: '<PERSON>', email: '<EMAIL>', role: 'Editor', status: 'Inactive', created_at: '2024-03-10' },
  { id: 4, name: '<PERSON>', email: '<EMAIL>', role: 'User', status: 'Active', created_at: '2024-04-05' },
  { id: 5, name: '<PERSON>', email: '<EMAIL>', role: 'Admin', status: 'Active', created_at: '2024-05-12' },
  { id: 6, name: '<PERSON>', email: '<EMAIL>', role: 'Editor', status: 'Active', created_at: '2024-06-18' },
  { id: 7, name: '<PERSON>', email: '<EMAIL>', role: 'User', status: 'Inactive', created_at: '2024-07-22' },
  { id: 8, name: 'Fiona Gallagher', email: '<EMAIL>', role: 'User', status: 'Active', created_at: '2024-08-30' },
  { id: 9, name: 'George Martin', email: '<EMAIL>', role: 'Editor', status: 'Active', created_at: '2024-09-15' },
  { id: 10, name: 'Hannah Baker', email: '<EMAIL>', role: 'Admin', status: 'Inactive', created_at: '2024-10-01' },
];

const columnHelper = createColumnHelper<Person>()

const exampleColumns = [
  columnHelper.accessor('id', {
    header: 'ID',
    cell: (info) => <div className="font-mono text-sm text-gray-600">{info.getValue()}</div>,
  }),
  columnHelper.accessor('name', {
    header: 'Name',
    cell: (info) => <div className="font-medium text-gray-900">{info.getValue()}</div>,
  }),
  columnHelper.accessor('email', {
    header: 'Email',
    cell: (info) => <div className="text-gray-600">{info.getValue()}</div>,
  }),
  columnHelper.accessor('role', {
    header: 'Role',
    cell: (info) => <div className="text-gray-600">{info.getValue()}</div>,
  }),
  columnHelper.accessor('status', {
    header: 'Status',
    cell: (info) => <div className="text-gray-600">{info.getValue()}</div>,
  }),
  columnHelper.accessor('created_at', {
    header: 'Created',
    cell: (info) => <div className="text-gray-600">{info.getValue()}</div>,
  }),
];

const Table = {
  Products: () => {
    return (
      <DataTable
        data={mockSupabaseData}
        columns={exampleColumns}
        enablePagination={true}
        enableSorting={true}
        enableFiltering={true}
        pageSize={5}
        searchPlaceholder="Search users by name, email, role..."
        emptyMessage="No users found. Try adjusting your search criteria."
      />
    );
  }
}
export default Table;